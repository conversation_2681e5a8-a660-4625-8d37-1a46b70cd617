#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入脚本 - 先导入一个小文件进行测试
"""

import os
import pandas as pd
import mysql.connector
from mysql.connector import Error
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_database_connection():
    """测试数据库连接"""
    try:
        connection = mysql.connector.connect(
            host='************',
            user='lxt',
            password='Lxt0307+',
            database='qq_day_sale',
            charset='utf8mb4'
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM qq_saledetail")
            count = cursor.fetchone()[0]
            logging.info(f"数据库连接成功，当前表中有 {count} 条记录")
            
            # 查看表结构
            cursor.execute("DESCRIBE qq_saledetail")
            columns = cursor.fetchall()
            logging.info("数据库表结构:")
            for col in columns:
                logging.info(f"  {col[0]}: {col[1]}")
            
            cursor.close()
            connection.close()
            return True
    except Error as e:
        logging.error(f"数据库连接失败: {e}")
        return False

def test_excel_reading():
    """测试Excel文件读取"""
    try:
        # 选择一个较小的文件进行测试
        test_files = [
            'data/20231001-1027销售明细账.xlsx',
            'data/20240103销售明细账.xlsx',
            'data/202401销售明细账.xlsx'
        ]
        
        for file_path in test_files:
            if os.path.exists(file_path):
                logging.info(f"测试读取文件: {file_path}")
                df = pd.read_excel(file_path)
                logging.info(f"文件包含 {len(df)} 行，{len(df.columns)} 列")
                logging.info(f"列名: {list(df.columns)}")
                
                # 显示前几行数据
                logging.info("前3行数据:")
                for i, row in df.head(3).iterrows():
                    logging.info(f"  行{i}: {dict(row)}")
                
                # 检查数据类型
                logging.info("数据类型:")
                for col, dtype in df.dtypes.items():
                    logging.info(f"  {col}: {dtype}")
                
                return True
        
        logging.error("没有找到测试文件")
        return False
        
    except Exception as e:
        logging.error(f"读取Excel文件失败: {e}")
        return False

def test_small_import():
    """测试导入少量数据"""
    try:
        from batch_import_xlsx import ExcelToMySQLImporter
        
        # 选择一个小文件
        test_file = None
        test_files = [
            'data/20240103销售明细账.xlsx',
            'data/202401销售明细账.xlsx',
            'data/20231001-1027销售明细账.xlsx'
        ]
        
        for file_path in test_files:
            if os.path.exists(file_path):
                # 检查文件大小
                df = pd.read_excel(file_path)
                if len(df) < 10000:  # 选择小于1万行的文件
                    test_file = file_path
                    break
        
        if not test_file:
            logging.error("没有找到合适的测试文件")
            return False
        
        logging.info(f"使用测试文件: {test_file}")
        
        # 创建导入器实例
        importer = ExcelToMySQLImporter()
        
        # 连接数据库
        if not importer.connect_database():
            return False
        
        try:
            # 导入测试文件
            success = importer.import_excel_file(test_file)
            
            if success:
                logging.info("测试导入成功！")
            else:
                logging.error("测试导入失败")
            
            return success
            
        finally:
            importer.close_connection()
            
    except Exception as e:
        logging.error(f"测试导入时出错: {e}")
        return False

def main():
    """主测试函数"""
    logging.info("开始测试...")
    
    # 1. 测试数据库连接
    logging.info("=== 测试数据库连接 ===")
    if not test_database_connection():
        logging.error("数据库连接测试失败，停止测试")
        return
    
    # 2. 测试Excel文件读取
    logging.info("\n=== 测试Excel文件读取 ===")
    if not test_excel_reading():
        logging.error("Excel文件读取测试失败，停止测试")
        return
    
    # 3. 测试小量数据导入
    logging.info("\n=== 测试数据导入 ===")
    if test_small_import():
        logging.info("所有测试通过！可以开始批量导入")
    else:
        logging.error("数据导入测试失败")

if __name__ == "__main__":
    main()
