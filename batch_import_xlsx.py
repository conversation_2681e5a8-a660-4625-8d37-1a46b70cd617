#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量导入Excel销售明细数据到MySQL数据库
根据db.txt配置文件导入data目录下的所有xlsx文件
"""

import os
import pandas as pd
import mysql.connector
from mysql.connector import Error
import logging
from datetime import datetime
import glob

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('import_log.txt', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ExcelToMySQLImporter:
    def __init__(self):
        # 数据库连接配置
        self.db_config = {
            'host': '************',
            'user': 'lxt',
            'password': 'Lxt0307+',
            'database': 'qq_day_sale',
            'charset': 'utf8mb4'
        }
        self.table_name = 'qq_saledetail'
        self.connection = None
        
        # Excel文件列名映射到数据库字段
        self.column_mapping = {
            # 直接匹配的字段
            '订单编号': '订单编号',
            '货品编号': '货品编号',
            '品牌': '品牌',
            '品名': '品名',
            '规格': '规格',
            '单位': '单位',
            '数量': '数量',
            '交易时间': '交易时间',
            '发货时间': '发货时间',
            '原始单号': '原始单号',
            '仓库': '仓库',
            '成本': '成本',
            '价格': '价格',
            '审核时间': '审核时间',
            '店铺': '店铺',
            '平台商品ID': '平台商品ID',

            # 新增的字段映射
            '物流方式': '物流方式',
            '货运单号': '货运单号',
            '州省': '州省',
            '区市': '区市',
            '区县': '区县',
            '预估重量': '预估重量',
            '货品总数': '货品总数',
            '货品样数': '货品样数',

            # 需要映射的字段
            '店铺名': '店铺',      # Excel中的"店铺名"映射到数据库的"店铺"
            '成交时间': '交易时间'   # Excel中的"成交时间"映射到数据库的"交易时间"
        }

        # 数据库中有但Excel中没有的字段，设置默认值
        self.default_values = {
            '交易规格': None,
            '订单货品备注': None
        }

    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            if self.connection.is_connected():
                logging.info("成功连接到MySQL数据库")
                return True
        except Error as e:
            logging.error(f"数据库连接失败: {e}")
            return False

    def close_connection(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logging.info("数据库连接已关闭")

    def process_excel_data(self, df):
        """处理Excel数据，转换为数据库格式"""
        # 过滤掉无效的列名（数字、nan等）
        valid_columns = []
        for col in df.columns:
            col_str = str(col)
            # 跳过数字列名、nan列名等无效列
            if (col_str.isdigit() or
                col_str.lower() in ['nan', 'unnamed'] or
                col_str.startswith('nan.') or
                col_str.startswith('Unnamed')):
                continue
            valid_columns.append(col)

        # 只保留有效列
        df_clean = df[valid_columns].copy()

        # 创建新的DataFrame用于数据库插入
        processed_data = pd.DataFrame()

        # 映射Excel列到数据库字段
        for excel_col, db_col in self.column_mapping.items():
            if db_col and excel_col in df_clean.columns:
                processed_data[db_col] = df_clean[excel_col]

        # 添加默认值字段
        for field, default_value in self.default_values.items():
            processed_data[field] = default_value

        # 数据类型转换和清理
        # 处理成本字段（可能包含"无权限查看"等文本）
        if '成本' in processed_data.columns:
            processed_data['成本'] = pd.to_numeric(processed_data['成本'], errors='coerce')

        # 处理数量字段
        if '数量' in processed_data.columns:
            processed_data['数量'] = pd.to_numeric(processed_data['数量'], errors='coerce')

        # 处理价格字段
        if '价格' in processed_data.columns:
            processed_data['价格'] = pd.to_numeric(processed_data['价格'], errors='coerce')

        # 处理预估重量字段
        if '预估重量' in processed_data.columns:
            processed_data['预估重量'] = pd.to_numeric(processed_data['预估重量'], errors='coerce')

        # 处理货品总数字段
        if '货品总数' in processed_data.columns:
            processed_data['货品总数'] = pd.to_numeric(processed_data['货品总数'], errors='coerce')

        # 处理货品样数字段
        if '货品样数' in processed_data.columns:
            processed_data['货品样数'] = pd.to_numeric(processed_data['货品样数'], errors='coerce')

        # 处理时间字段
        for time_col in ['交易时间', '发货时间', '审核时间']:
            if time_col in processed_data.columns:
                processed_data[time_col] = pd.to_datetime(processed_data[time_col], errors='coerce')

        return processed_data

    def insert_data_batch(self, data, batch_size=1000):
        """批量插入数据到数据库"""
        if not self.connection or not self.connection.is_connected():
            logging.error("数据库未连接")
            return False
        
        cursor = self.connection.cursor()
        
        try:
            # 构建插入SQL语句
            columns = list(data.columns)
            placeholders = ', '.join(['%s'] * len(columns))
            sql = f"INSERT INTO {self.table_name} ({', '.join(columns)}) VALUES ({placeholders})"
            
            # 转换数据为列表格式
            data_list = []
            for _, row in data.iterrows():
                row_data = []
                for col in columns:
                    value = row[col]
                    if pd.isna(value):
                        row_data.append(None)
                    else:
                        row_data.append(value)
                data_list.append(tuple(row_data))
            
            # 批量插入
            total_rows = len(data_list)
            inserted_rows = 0
            
            for i in range(0, total_rows, batch_size):
                batch = data_list[i:i + batch_size]
                cursor.executemany(sql, batch)
                self.connection.commit()
                inserted_rows += len(batch)
                logging.info(f"已插入 {inserted_rows}/{total_rows} 行数据")
            
            logging.info(f"成功插入 {total_rows} 行数据")
            return True
            
        except Error as e:
            logging.error(f"数据插入失败: {e}")
            self.connection.rollback()
            return False
        finally:
            cursor.close()

    def import_excel_file(self, file_path):
        """导入单个Excel文件"""
        try:
            logging.info(f"开始处理文件: {file_path}")
            
            # 读取Excel文件
            df = pd.read_excel(file_path)
            logging.info(f"文件包含 {len(df)} 行数据")
            
            if len(df) == 0:
                logging.warning(f"文件 {file_path} 为空，跳过")
                return True
            
            # 处理数据
            processed_data = self.process_excel_data(df)
            
            # 插入数据库
            success = self.insert_data_batch(processed_data)
            
            if success:
                logging.info(f"文件 {file_path} 导入成功")
            else:
                logging.error(f"文件 {file_path} 导入失败")
            
            return success
            
        except Exception as e:
            logging.error(f"处理文件 {file_path} 时出错: {e}")
            return False

    def batch_import_all_files(self, data_dir='data'):
        """批量导入所有Excel文件"""
        if not self.connect_database():
            return False
        
        try:
            # 获取所有xlsx文件
            xlsx_files = glob.glob(os.path.join(data_dir, '*.xlsx'))
            xlsx_files.sort()  # 按文件名排序
            
            logging.info(f"找到 {len(xlsx_files)} 个Excel文件")
            
            success_count = 0
            failed_files = []
            
            for file_path in xlsx_files:
                if self.import_excel_file(file_path):
                    success_count += 1
                else:
                    failed_files.append(file_path)
            
            logging.info(f"导入完成: 成功 {success_count} 个文件，失败 {len(failed_files)} 个文件")
            
            if failed_files:
                logging.error("失败的文件:")
                for file_path in failed_files:
                    logging.error(f"  - {file_path}")
            
            return len(failed_files) == 0
            
        finally:
            self.close_connection()

def main():
    """主函数"""
    logging.info("开始批量导入Excel数据")
    
    importer = ExcelToMySQLImporter()
    success = importer.batch_import_all_files()
    
    if success:
        logging.info("所有文件导入成功！")
    else:
        logging.error("部分文件导入失败，请查看日志")

if __name__ == "__main__":
    main()
