#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步数据库表结构与Excel文件
扫描所有Excel文件，分析字段，更新数据库表结构
"""

import os
import pandas as pd
import mysql.connector
from mysql.connector import Error
import logging
import glob
from collections import defaultdict
import numpy as np

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sync_structure_log.txt', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class DatabaseStructureSync:
    def __init__(self):
        # 数据库连接配置
        self.db_config = {
            'host': '************',
            'user': 'lxt',
            'password': 'Lxt0307+',
            'database': 'qq_day_sale',
            'charset': 'utf8mb4'
        }
        self.table_name = 'qq_saledetail'
        self.connection = None
        
        # 存储分析结果
        self.excel_fields = {}  # 字段名 -> 数据类型信息
        self.db_fields = {}     # 当前数据库字段
        self.missing_fields = {}  # 需要添加的字段

    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            if self.connection.is_connected():
                logging.info("成功连接到MySQL数据库")
                return True
        except Error as e:
            logging.error(f"数据库连接失败: {e}")
            return False

    def close_connection(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logging.info("数据库连接已关闭")

    def scan_excel_files(self, data_dir='data'):
        """扫描所有Excel文件，收集字段信息"""
        logging.info("开始扫描Excel文件...")
        
        xlsx_files = glob.glob(os.path.join(data_dir, '*.xlsx'))
        logging.info(f"找到 {len(xlsx_files)} 个Excel文件")
        
        field_stats = defaultdict(lambda: {
            'count': 0,
            'data_types': set(),
            'sample_values': [],
            'max_length': 0,
            'has_null': False,
            'numeric_values': [],
            'datetime_values': []
        })
        
        processed_files = 0
        for file_path in xlsx_files:
            try:
                logging.info(f"处理文件: {os.path.basename(file_path)}")
                df = pd.read_excel(file_path)
                
                for column in df.columns:
                    field_stats[column]['count'] += 1
                    
                    # 分析数据类型
                    dtype = str(df[column].dtype)
                    field_stats[column]['data_types'].add(dtype)
                    
                    # 收集样本值
                    non_null_values = df[column].dropna()
                    if len(non_null_values) > 0:
                        sample_values = non_null_values.head(5).tolist()
                        field_stats[column]['sample_values'].extend(sample_values)
                        
                        # 计算最大长度（对于字符串）
                        if dtype == 'object':
                            max_len = non_null_values.astype(str).str.len().max()
                            field_stats[column]['max_length'] = max(
                                field_stats[column]['max_length'], max_len
                            )
                        
                        # 收集数值型数据
                        if dtype in ['int64', 'float64']:
                            field_stats[column]['numeric_values'].extend(
                                non_null_values.tolist()[:10]
                            )
                        
                        # 收集日期时间数据
                        if 'datetime' in dtype:
                            field_stats[column]['datetime_values'].extend(
                                non_null_values.tolist()[:5]
                            )
                    
                    # 检查是否有空值
                    if df[column].isnull().any():
                        field_stats[column]['has_null'] = True
                
                processed_files += 1
                if processed_files % 10 == 0:
                    logging.info(f"已处理 {processed_files}/{len(xlsx_files)} 个文件")
                    
            except Exception as e:
                logging.error(f"处理文件 {file_path} 时出错: {e}")
                continue
        
        # 转换为普通字典并分析数据类型
        self.excel_fields = {}
        for field, stats in field_stats.items():
            mysql_type = self._determine_mysql_type(field, stats)
            self.excel_fields[field] = {
                'mysql_type': mysql_type,
                'stats': dict(stats),
                'appears_in_files': stats['count']
            }
        
        logging.info(f"扫描完成，发现 {len(self.excel_fields)} 个不同的字段")
        return True

    def _determine_mysql_type(self, field_name, stats):
        """根据字段统计信息确定MySQL数据类型"""
        data_types = stats['data_types']
        max_length = stats['max_length']
        sample_values = stats['sample_values'][:10]  # 只看前10个样本
        
        # 日期时间类型
        if any('datetime' in dt for dt in data_types):
            return 'DATETIME'
        
        # 数值类型
        if data_types.issubset({'int64', 'float64'}):
            # 检查是否都是整数
            if 'int64' in data_types and 'float64' not in data_types:
                # 检查数值范围
                numeric_values = stats['numeric_values']
                if numeric_values:
                    max_val = max(numeric_values)
                    min_val = min(numeric_values)
                    if min_val >= 0 and max_val < 4294967295:
                        return 'INT UNSIGNED'
                    else:
                        return 'BIGINT'
            else:
                return 'FLOAT'
        
        # 文本类型
        if 'object' in data_types:
            # 检查样本值，看是否包含特殊内容
            sample_str = str(sample_values)
            
            # 如果最大长度很小，使用VARCHAR
            if max_length <= 50:
                return f'VARCHAR({max(max_length * 2, 100)})'
            elif max_length <= 255:
                return f'VARCHAR({max_length * 2})'
            else:
                return 'TEXT'
        
        # 默认使用TEXT
        return 'TEXT'

    def get_current_db_structure(self):
        """获取当前数据库表结构"""
        if not self.connection or not self.connection.is_connected():
            logging.error("数据库未连接")
            return False
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(f"DESCRIBE {self.table_name}")
            columns = cursor.fetchall()
            
            self.db_fields = {}
            for col in columns:
                field_name = col[0]
                field_type = col[1]
                nullable = col[2]
                key = col[3]
                default = col[4]
                extra = col[5]
                
                self.db_fields[field_name] = {
                    'type': field_type,
                    'nullable': nullable,
                    'key': key,
                    'default': default,
                    'extra': extra
                }
            
            logging.info(f"当前数据库表有 {len(self.db_fields)} 个字段")
            cursor.close()
            return True
            
        except Error as e:
            logging.error(f"获取数据库表结构失败: {e}")
            return False

    def analyze_missing_fields(self):
        """分析缺失的字段"""
        self.missing_fields = {}
        
        for field_name, field_info in self.excel_fields.items():
            if field_name not in self.db_fields:
                self.missing_fields[field_name] = field_info
        
        logging.info(f"发现 {len(self.missing_fields)} 个缺失字段:")
        for field_name, field_info in self.missing_fields.items():
            logging.info(f"  - {field_name}: {field_info['mysql_type']} "
                        f"(出现在 {field_info['appears_in_files']} 个文件中)")
        
        return len(self.missing_fields) > 0

    def add_missing_fields(self):
        """添加缺失的字段到数据库表"""
        if not self.missing_fields:
            logging.info("没有需要添加的字段")
            return True
        
        if not self.connection or not self.connection.is_connected():
            logging.error("数据库未连接")
            return False
        
        try:
            cursor = self.connection.cursor()
            
            for field_name, field_info in self.missing_fields.items():
                mysql_type = field_info['mysql_type']
                
                # 构建ALTER TABLE语句
                sql = f"ALTER TABLE {self.table_name} ADD COLUMN `{field_name}` {mysql_type}"
                
                logging.info(f"执行SQL: {sql}")
                cursor.execute(sql)
                self.connection.commit()
                logging.info(f"成功添加字段: {field_name}")
            
            cursor.close()
            logging.info("所有缺失字段添加完成")
            return True
            
        except Error as e:
            logging.error(f"添加字段失败: {e}")
            self.connection.rollback()
            return False

    def verify_structure_update(self):
        """验证表结构更新是否成功"""
        logging.info("验证表结构更新...")
        
        if not self.get_current_db_structure():
            return False
        
        # 检查所有Excel字段是否都存在于数据库中
        missing_after_update = []
        for field_name in self.excel_fields.keys():
            if field_name not in self.db_fields:
                missing_after_update.append(field_name)
        
        if missing_after_update:
            logging.error(f"以下字段仍然缺失: {missing_after_update}")
            return False
        else:
            logging.info("表结构更新验证成功，所有Excel字段都已存在于数据库中")
            return True

    def generate_field_mapping(self):
        """生成更新后的字段映射"""
        logging.info("生成字段映射...")
        
        mapping = {}
        for field_name in self.excel_fields.keys():
            mapping[field_name] = field_name  # Excel字段名 -> 数据库字段名
        
        logging.info("字段映射:")
        for excel_field, db_field in mapping.items():
            appears_in = self.excel_fields[excel_field]['appears_in_files']
            logging.info(f"  '{excel_field}' -> '{db_field}' (出现在{appears_in}个文件中)")
        
        return mapping

    def run_sync(self):
        """执行完整的同步流程"""
        logging.info("开始数据库结构同步...")
        
        # 1. 扫描Excel文件
        if not self.scan_excel_files():
            logging.error("扫描Excel文件失败")
            return False
        
        # 2. 连接数据库并获取当前结构
        if not self.connect_database():
            return False
        
        try:
            if not self.get_current_db_structure():
                return False
            
            # 3. 分析缺失字段
            if not self.analyze_missing_fields():
                logging.info("没有缺失字段，表结构已是最新")
                return True
            
            # 4. 添加缺失字段
            if not self.add_missing_fields():
                return False
            
            # 5. 验证更新
            if not self.verify_structure_update():
                return False
            
            # 6. 生成字段映射
            self.generate_field_mapping()
            
            logging.info("数据库结构同步完成！")
            return True
            
        finally:
            self.close_connection()

def main():
    """主函数"""
    sync = DatabaseStructureSync()
    success = sync.run_sync()
    
    if success:
        logging.info("数据库结构同步成功！现在可以运行批量导入脚本了。")
    else:
        logging.error("数据库结构同步失败，请检查日志")

if __name__ == "__main__":
    main()
