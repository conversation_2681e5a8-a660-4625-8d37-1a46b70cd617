# 电商数据库恢复 - Excel批量导入工具

## 项目概述

本项目提供了一套完整的工具来批量导入Excel销售明细数据到MySQL数据库。根据`db.txt`配置文件，自动处理`data`目录下的所有xlsx文件并导入到指定的数据库表中。

## 文件说明

### 核心脚本

1. **`sync_database_structure.py`** - 数据库结构同步脚本
   - 扫描所有Excel文件，分析字段结构
   - 自动更新数据库表结构，添加缺失字段
   - 确保数据库表与Excel文件字段完全匹配

2. **`final_batch_import.py`** - 最终批量导入脚本
   - 批量导入所有Excel文件到数据库
   - 支持数据类型转换和清理
   - 提供详细的进度日志和错误处理

3. **`test_updated_import.py`** - 测试脚本
   - 验证数据库连接和表结构
   - 测试Excel文件处理功能
   - 小规模数据导入测试

### 配置文件

- **`db.txt`** - 数据库连接配置
  ```
  数据库连接信息：
  - 主机地址：192.168.0.99
  - 用户名：lxt
  - 密码：Lxt0307+
  - 数据库名：qq_day_sale
  - 目标表：qq_saledetail
  ```

## 数据库表结构

经过同步后，`qq_saledetail`表包含以下18个字段：

| 序号 | 字段名 | 数据类型 | 说明 |
|------|--------|----------|------|
| 1 | 订单编号 | text | 订单唯一标识 |
| 2 | 货品编号 | text | 商品编号 |
| 3 | 品牌 | text | 商品品牌 |
| 4 | 品名 | text | 商品名称 |
| 5 | 规格 | text | 商品规格 |
| 6 | 单位 | text | 计量单位 |
| 7 | 数量 | float | 销售数量 |
| 8 | 交易时间 | datetime | 交易发生时间 |
| 9 | 发货时间 | datetime | 商品发货时间 |
| 10 | 原始单号 | text | 原始订单号 |
| 11 | 仓库 | text | 发货仓库 |
| 12 | 成本 | float | 商品成本 |
| 13 | 价格 | float | 销售价格 |
| 14 | 店铺 | text | 销售店铺 |
| 15 | 平台商品ID | float | 平台商品标识 |
| 16 | 交易规格 | text | 交易规格信息 |
| 17 | 订单货品备注 | text | 订单备注 |
| 18 | 审核时间 | datetime | 订单审核时间 |

## 使用步骤

### 1. 环境准备

确保已安装必要的Python包：
```bash
pip3 install pandas openpyxl mysql-connector-python
```

### 2. 数据库结构同步（首次运行）

```bash
python3 sync_database_structure.py
```

这个脚本会：
- 扫描所有Excel文件，分析字段结构
- 连接数据库，获取当前表结构
- 自动添加缺失的字段
- 验证表结构更新是否成功

### 3. 测试导入功能

```bash
python3 test_updated_import.py
```

这个脚本会：
- 测试数据库连接
- 验证Excel文件处理功能
- 进行小规模数据导入测试

### 4. 批量导入数据

```bash
python3 final_batch_import.py
```

这个脚本会：
- 批量处理`data`目录下的所有xlsx文件
- 自动进行数据类型转换和清理
- 提供详细的进度信息和日志
- 生成带时间戳的日志文件

## 数据处理特性

### 数据清理
- 自动过滤无效列名（数字、nan等）
- 处理"无权限查看"等特殊文本
- 数值字段自动转换为数字类型
- 时间字段自动转换为datetime类型

### 错误处理
- 数据类型转换错误自动处理（设为NULL）
- 文件读取错误跳过并记录
- 数据库连接异常自动重试
- 详细的错误日志记录

### 性能优化
- 批量插入（每批1000条记录）
- 进度实时显示
- 内存使用优化
- 支持大文件处理

## 日志文件

脚本运行时会生成详细的日志文件：
- `import_log_YYYYMMDD_HHMMSS.txt` - 批量导入日志
- `sync_structure_log.txt` - 结构同步日志

日志包含：
- 处理进度信息
- 数据统计信息
- 错误详情
- 性能指标

## 注意事项

1. **首次运行**：必须先运行`sync_database_structure.py`同步表结构
2. **数据备份**：建议在大批量导入前备份数据库
3. **网络连接**：确保与数据库服务器的网络连接稳定
4. **磁盘空间**：确保有足够的磁盘空间存储日志文件
5. **文件格式**：Excel文件必须是.xlsx格式

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查网络连接
   - 验证数据库配置信息
   - 确认数据库服务运行状态

2. **Excel文件读取失败**
   - 检查文件是否损坏
   - 确认文件格式为.xlsx
   - 验证文件权限

3. **数据插入失败**
   - 检查表结构是否正确
   - 验证数据类型匹配
   - 查看详细错误日志

### 性能调优

- 调整批量插入大小（batch_size参数）
- 优化数据库连接池设置
- 考虑分批处理大量文件

## 项目状态

✅ 数据库结构同步完成
✅ 批量导入功能测试通过
✅ 错误处理机制完善
✅ 日志记录功能完整
✅ 性能优化实施

项目已准备好进行生产环境的批量数据导入。
