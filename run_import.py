#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键运行导入脚本
自动执行完整的导入流程
"""

import os
import sys
import logging
import subprocess
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def run_command(command, description):
    """运行命令并返回结果"""
    logging.info(f"开始执行: {description}")
    logging.info(f"命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            logging.info(f"✅ {description} 执行成功")
            if result.stdout:
                logging.info("输出:")
                for line in result.stdout.strip().split('\n'):
                    logging.info(f"  {line}")
            return True
        else:
            logging.error(f"❌ {description} 执行失败")
            if result.stderr:
                logging.error("错误信息:")
                for line in result.stderr.strip().split('\n'):
                    logging.error(f"  {line}")
            return False
            
    except Exception as e:
        logging.error(f"❌ 执行 {description} 时出现异常: {e}")
        return False

def check_prerequisites():
    """检查运行前提条件"""
    logging.info("检查运行前提条件...")
    
    # 检查Python包
    required_packages = ['pandas', 'openpyxl', 'mysql-connector-python']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            logging.info(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            logging.error(f"❌ {package} 未安装")
    
    if missing_packages:
        logging.error("请先安装缺失的包:")
        for package in missing_packages:
            logging.error(f"  pip3 install {package}")
        return False
    
    # 检查必要文件
    required_files = ['db.txt', 'sync_database_structure.py', 'final_batch_import.py']
    for file_path in required_files:
        if os.path.exists(file_path):
            logging.info(f"✅ {file_path} 存在")
        else:
            logging.error(f"❌ {file_path} 不存在")
            return False
    
    # 检查data目录
    if os.path.exists('data') and os.path.isdir('data'):
        xlsx_files = [f for f in os.listdir('data') if f.endswith('.xlsx')]
        logging.info(f"✅ data目录存在，包含 {len(xlsx_files)} 个xlsx文件")
    else:
        logging.error("❌ data目录不存在")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("电商数据库恢复 - Excel批量导入工具")
    print("=" * 60)
    
    start_time = datetime.now()
    logging.info(f"开始时间: {start_time}")
    
    # 1. 检查前提条件
    logging.info("\n=== 步骤1: 检查前提条件 ===")
    if not check_prerequisites():
        logging.error("前提条件检查失败，请解决问题后重新运行")
        return False
    
    # 2. 询问用户是否继续
    print("\n" + "=" * 60)
    print("准备开始数据导入流程，这将包括:")
    print("1. 同步数据库表结构")
    print("2. 测试导入功能")
    print("3. 批量导入所有Excel文件")
    print("=" * 60)
    
    response = input("是否继续? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        logging.info("用户取消操作")
        return False
    
    # 3. 同步数据库结构
    logging.info("\n=== 步骤2: 同步数据库表结构 ===")
    if not run_command("python3 sync_database_structure.py", "数据库结构同步"):
        logging.error("数据库结构同步失败，停止执行")
        return False
    
    # 4. 测试导入功能
    logging.info("\n=== 步骤3: 测试导入功能 ===")
    if not run_command("python3 test_updated_import.py", "导入功能测试"):
        logging.error("导入功能测试失败，停止执行")
        return False
    
    # 5. 最终确认
    print("\n" + "=" * 60)
    print("测试通过！准备开始批量导入所有Excel文件")
    print("注意：这个过程可能需要较长时间，请耐心等待")
    print("=" * 60)
    
    response = input("确认开始批量导入? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        logging.info("用户取消批量导入")
        return False
    
    # 6. 批量导入
    logging.info("\n=== 步骤4: 批量导入Excel文件 ===")
    if not run_command("python3 final_batch_import.py", "批量导入Excel文件"):
        logging.error("批量导入失败")
        return False
    
    # 7. 完成
    end_time = datetime.now()
    elapsed_time = end_time - start_time
    
    logging.info("\n" + "=" * 60)
    logging.info("🎉 所有步骤执行完成！")
    logging.info(f"开始时间: {start_time}")
    logging.info(f"结束时间: {end_time}")
    logging.info(f"总耗时: {elapsed_time}")
    logging.info("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logging.info("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        logging.error(f"执行过程中出现异常: {e}")
        sys.exit(1)
