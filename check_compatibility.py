#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表结构与Excel文件字段的兼容性
"""

import os
import pandas as pd
import mysql.connector
from mysql.connector import Error
import glob
from collections import defaultdict

class CompatibilityChecker:
    def __init__(self):
        # 数据库连接配置
        self.db_config = {
            'host': '************',
            'user': 'lxt',
            'password': 'Lxt0307+',
            'database': 'qq_day_sale',
            'charset': 'utf8mb4'
        }
        self.table_name = 'qq_saledetail'
        self.db_columns = {}
        self.excel_files_columns = {}
        
    def get_database_structure(self):
        """获取数据库表结构"""
        try:
            connection = mysql.connector.connect(**self.db_config)
            cursor = connection.cursor()
            
            cursor.execute(f'DESCRIBE {self.table_name}')
            columns = cursor.fetchall()
            
            for col in columns:
                field, type_info, null, key, default, extra = col
                self.db_columns[field] = {
                    'type': type_info,
                    'null': null,
                    'key': key,
                    'default': default,
                    'extra': extra
                }
            
            print(f"数据库表 {self.table_name} 包含 {len(self.db_columns)} 个字段:")
            for field, info in self.db_columns.items():
                print(f"  - {field}: {info['type']}")
            
            return True
            
        except Error as e:
            print(f"数据库连接失败: {e}")
            return False
        finally:
            if 'connection' in locals() and connection.is_connected():
                cursor.close()
                connection.close()
    
    def analyze_excel_files(self, data_dir='data'):
        """分析所有Excel文件的字段结构"""
        xlsx_files = glob.glob(os.path.join(data_dir, '*.xlsx'))
        xlsx_files.sort()
        
        print(f"\n分析 {len(xlsx_files)} 个Excel文件:")
        
        for file_path in xlsx_files:
            try:
                df = pd.read_excel(file_path)
                filename = os.path.basename(file_path)
                
                # 过滤有效列名
                valid_columns = []
                for col in df.columns:
                    col_str = str(col)
                    if (not col_str.isdigit() and 
                        col_str.lower() not in ['nan', 'unnamed'] and
                        not col_str.startswith('nan.') and
                        not col_str.startswith('Unnamed')):
                        valid_columns.append(col)
                
                self.excel_files_columns[filename] = {
                    'columns': valid_columns,
                    'row_count': len(df),
                    'sample_data': df[valid_columns].head(2) if valid_columns else None
                }
                
                print(f"  {filename}: {len(valid_columns)} 列, {len(df)} 行")
                for col in valid_columns:
                    print(f"    - {col}")
                
            except Exception as e:
                print(f"  错误处理文件 {file_path}: {e}")
    
    def check_compatibility(self):
        """检查兼容性"""
        print("\n" + "="*80)
        print("兼容性分析报告")
        print("="*80)
        
        # 统计所有Excel文件中出现的字段
        all_excel_columns = set()
        column_frequency = defaultdict(int)
        
        for filename, info in self.excel_files_columns.items():
            for col in info['columns']:
                all_excel_columns.add(col)
                column_frequency[col] += 1
        
        print(f"\n1. Excel文件字段统计:")
        print(f"   总共发现 {len(all_excel_columns)} 个不同的字段")
        for col, freq in sorted(column_frequency.items(), key=lambda x: x[1], reverse=True):
            print(f"   - {col}: 出现在 {freq}/{len(self.excel_files_columns)} 个文件中")
        
        # 检查数据库中存在但Excel中不存在的字段
        print(f"\n2. 数据库中有但Excel文件中没有的字段:")
        db_only_fields = set(self.db_columns.keys()) - all_excel_columns
        if db_only_fields:
            for field in sorted(db_only_fields):
                print(f"   - {field} ({self.db_columns[field]['type']})")
                if self.db_columns[field]['null'] == 'NO':
                    print(f"     ⚠️  警告: 此字段不允许为空，需要提供默认值")
        else:
            print("   ✅ 无")
        
        # 检查Excel中存在但数据库中不存在的字段
        print(f"\n3. Excel文件中有但数据库中没有的字段:")
        excel_only_fields = all_excel_columns - set(self.db_columns.keys())
        if excel_only_fields:
            for field in sorted(excel_only_fields):
                files_with_field = [f for f, info in self.excel_files_columns.items() 
                                  if field in info['columns']]
                print(f"   - {field}: 出现在 {len(files_with_field)} 个文件中")
                print(f"     文件: {', '.join(files_with_field[:3])}{'...' if len(files_with_field) > 3 else ''}")
        else:
            print("   ✅ 无")
        
        # 检查字段匹配情况
        print(f"\n4. 字段匹配情况:")
        matched_fields = all_excel_columns & set(self.db_columns.keys())
        print(f"   匹配的字段 ({len(matched_fields)} 个):")
        for field in sorted(matched_fields):
            print(f"   ✅ {field}")
        
        # 数据类型兼容性检查
        print(f"\n5. 数据类型兼容性分析:")
        self.check_data_type_compatibility(matched_fields)
        
        # 生成兼容性总结
        print(f"\n6. 兼容性总结:")
        total_excel_fields = len(all_excel_columns)
        total_db_fields = len(self.db_columns)
        matched_count = len(matched_fields)
        
        print(f"   - Excel字段总数: {total_excel_fields}")
        print(f"   - 数据库字段总数: {total_db_fields}")
        print(f"   - 匹配字段数: {matched_count}")
        print(f"   - 匹配率: {matched_count/total_excel_fields*100:.1f}% (Excel -> DB)")
        print(f"   - 覆盖率: {matched_count/total_db_fields*100:.1f}% (DB <- Excel)")
        
        if len(db_only_fields) == 0 and len(excel_only_fields) == 0:
            print("   ✅ 完全兼容")
        elif len(excel_only_fields) == 0:
            print("   ⚠️  部分兼容 - 数据库有额外字段")
        else:
            print("   ❌ 不完全兼容 - 存在字段不匹配")
    
    def check_data_type_compatibility(self, matched_fields):
        """检查数据类型兼容性"""
        for field in sorted(matched_fields):
            db_type = self.db_columns[field]['type']
            print(f"   {field}:")
            print(f"     数据库类型: {db_type}")
            
            # 检查Excel中该字段的数据类型
            sample_values = []
            for filename, info in self.excel_files_columns.items():
                if field in info['columns'] and info['sample_data'] is not None:
                    if field in info['sample_data'].columns:
                        values = info['sample_data'][field].dropna().tolist()
                        sample_values.extend(values[:2])  # 取前2个非空值
            
            if sample_values:
                print(f"     Excel样本值: {sample_values[:5]}")  # 显示前5个样本值
                
                # 简单的类型兼容性检查
                if 'text' in db_type.lower():
                    print("     ✅ 文本类型，兼容")
                elif 'float' in db_type.lower() or 'decimal' in db_type.lower():
                    try:
                        numeric_values = [pd.to_numeric(v, errors='coerce') for v in sample_values[:3]]
                        if any(pd.isna(v) for v in numeric_values):
                            print("     ⚠️  包含非数字值，可能需要数据清理")
                        else:
                            print("     ✅ 数值类型，兼容")
                    except:
                        print("     ⚠️  数值类型检查失败")
                elif 'datetime' in db_type.lower():
                    try:
                        date_values = [pd.to_datetime(v, errors='coerce') for v in sample_values[:3]]
                        if any(pd.isna(v) for v in date_values):
                            print("     ⚠️  包含无效日期值，可能需要数据清理")
                        else:
                            print("     ✅ 日期类型，兼容")
                    except:
                        print("     ⚠️  日期类型检查失败")
            else:
                print("     ⚠️  无样本数据")

def main():
    """主函数"""
    print("开始检查数据库与Excel文件的兼容性...")
    
    checker = CompatibilityChecker()
    
    # 获取数据库结构
    if not checker.get_database_structure():
        print("无法获取数据库结构，退出检查")
        return
    
    # 分析Excel文件
    checker.analyze_excel_files()
    
    # 检查兼容性
    checker.check_compatibility()

if __name__ == "__main__":
    main()
