#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为数据库表添加新的字段列
"""

import mysql.connector
from mysql.connector import Error
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('add_columns_log.txt', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class DatabaseColumnManager:
    def __init__(self):
        # 数据库连接配置
        self.db_config = {
            'host': '************',
            'user': 'lxt',
            'password': 'Lxt0307+',
            'database': 'qq_day_sale',
            'charset': 'utf8mb4'
        }
        self.table_name = 'qq_saledetail'
        
        # 需要添加的新字段
        self.new_columns = {
            '物流方式': 'TEXT',
            '货运单号': 'TEXT',
            '州省': 'TEXT',
            '区市': 'TEXT',
            '区县': 'TEXT',
            '预估重量': 'FLOAT',
            '货品总数': 'INT',
            '货品样数': 'INT'
        }
    
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            if self.connection.is_connected():
                logging.info("成功连接到MySQL数据库")
                return True
        except Error as e:
            logging.error(f"数据库连接失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if hasattr(self, 'connection') and self.connection.is_connected():
            self.connection.close()
            logging.info("数据库连接已关闭")
    
    def get_existing_columns(self):
        """获取现有的表字段"""
        try:
            cursor = self.connection.cursor()
            cursor.execute(f'DESCRIBE {self.table_name}')
            columns = cursor.fetchall()
            existing_columns = [col[0] for col in columns]
            cursor.close()
            return existing_columns
        except Error as e:
            logging.error(f"获取表结构失败: {e}")
            return []
    
    def add_columns(self):
        """添加新字段到数据库表"""
        if not self.connect_database():
            return False
        
        try:
            # 获取现有字段
            existing_columns = self.get_existing_columns()
            logging.info(f"当前表字段: {existing_columns}")
            
            cursor = self.connection.cursor()
            added_columns = []
            skipped_columns = []
            
            for column_name, column_type in self.new_columns.items():
                if column_name in existing_columns:
                    logging.info(f"字段 '{column_name}' 已存在，跳过")
                    skipped_columns.append(column_name)
                    continue
                
                try:
                    # 添加新字段
                    sql = f"ALTER TABLE {self.table_name} ADD COLUMN `{column_name}` {column_type}"
                    logging.info(f"执行SQL: {sql}")
                    cursor.execute(sql)
                    self.connection.commit()
                    logging.info(f"成功添加字段: {column_name} ({column_type})")
                    added_columns.append(column_name)
                    
                except Error as e:
                    logging.error(f"添加字段 '{column_name}' 失败: {e}")
                    self.connection.rollback()
            
            cursor.close()
            
            # 输出结果
            logging.info(f"操作完成:")
            logging.info(f"  - 成功添加 {len(added_columns)} 个字段: {added_columns}")
            logging.info(f"  - 跳过 {len(skipped_columns)} 个已存在字段: {skipped_columns}")
            
            return len(added_columns) > 0
            
        except Error as e:
            logging.error(f"添加字段过程中出错: {e}")
            return False
        finally:
            self.close_connection()
    
    def verify_columns(self):
        """验证字段是否添加成功"""
        if not self.connect_database():
            return False
        
        try:
            existing_columns = self.get_existing_columns()
            logging.info(f"\n验证结果:")
            logging.info(f"当前表总字段数: {len(existing_columns)}")
            
            for column_name in self.new_columns.keys():
                if column_name in existing_columns:
                    logging.info(f"  ✅ {column_name}: 存在")
                else:
                    logging.info(f"  ❌ {column_name}: 不存在")
            
            return True
            
        except Error as e:
            logging.error(f"验证字段失败: {e}")
            return False
        finally:
            self.close_connection()

def main():
    """主函数"""
    logging.info("开始添加数据库新字段...")
    
    manager = DatabaseColumnManager()
    
    # 添加字段
    success = manager.add_columns()
    
    if success:
        logging.info("字段添加操作完成")
    else:
        logging.error("字段添加操作失败")
    
    # 验证字段
    manager.verify_columns()

if __name__ == "__main__":
    main()
