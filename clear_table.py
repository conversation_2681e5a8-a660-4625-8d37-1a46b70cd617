#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清空数据库表数据的脚本
"""

import mysql.connector
from mysql.connector import Error

def clear_table():
    """清空qq_saledetail表的数据"""
    
    # 数据库连接配置
    config = {
        'host': '************',
        'user': 'lxt',
        'password': 'Lxt0307+',
        'database': 'qq_day_sale',
        'charset': 'utf8mb4'
    }
    
    connection = None
    cursor = None
    
    try:
        # 建立数据库连接
        print("正在连接到数据库...")
        connection = mysql.connector.connect(**config)
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # 首先查看表中的记录数
            cursor.execute("SELECT COUNT(*) FROM qq_saledetail")
            count_before = cursor.fetchone()[0]
            print(f"清空前表中记录数: {count_before}")
            
            if count_before > 0:
                # 清空表数据 - 使用TRUNCATE TABLE更高效
                print("正在清空表数据...")
                cursor.execute("TRUNCATE TABLE qq_saledetail")
                connection.commit()
                
                # 验证清空结果
                cursor.execute("SELECT COUNT(*) FROM qq_saledetail")
                count_after = cursor.fetchone()[0]
                print(f"清空后表中记录数: {count_after}")
                
                if count_after == 0:
                    print("✅ 表数据清空成功！")
                else:
                    print("❌ 表数据清空失败！")
            else:
                print("表中没有数据，无需清空。")
                
    except Error as e:
        print(f"❌ 数据库操作出错: {e}")
        if connection and connection.is_connected():
            connection.rollback()
            
    finally:
        # 关闭数据库连接
        if cursor:
            cursor.close()
        if connection and connection.is_connected():
            connection.close()
            print("数据库连接已关闭。")

if __name__ == "__main__":
    clear_table()
