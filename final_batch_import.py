#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版本的批量导入脚本
根据db.txt配置文件批量导入data目录下的所有xlsx文件到MySQL数据库
"""

import os
import pandas as pd
import mysql.connector
from mysql.connector import Error
import logging
from datetime import datetime
import glob
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'import_log_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class FinalExcelToMySQLImporter:
    def __init__(self):
        # 数据库连接配置（从db.txt读取）
        self.db_config = {
            'host': '************',
            'user': 'lxt',
            'password': 'Lxt0307+',
            'database': 'qq_day_sale',
            'charset': 'utf8mb4'
        }
        self.table_name = 'qq_saledetail_copy1'  # 更改为备份表
        self.connection = None

        # Excel文件列名映射到数据库字段
        self.column_mapping = {
            # 直接匹配的字段
            '订单编号': '订单编号',
            '货品编号': '货品编号',
            '品牌': '品牌',
            '品名': '品名',
            '规格': '规格',
            '单位': '单位',
            '数量': '数量',
            '交易时间': '交易时间',
            '发货时间': '发货时间',
            '原始单号': '原始单号',
            '仓库': '仓库',
            '成本': '成本',
            '价格': '价格',
            '审核时间': '审核时间',
            '店铺': '店铺',
            '平台商品ID': '平台商品ID',

            # 新增的字段映射
            '物流方式': '物流方式',
            '货运单号': '货运单号',
            '州省': '州省',
            '区市': '区市',
            '区县': '区县',
            '预估重量': '预估重量',
            '货品总数': '货品总数',
            '货品样数': '货品样数',

            # 需要映射的字段
            '店铺名': '店铺',      # Excel中的"店铺名"映射到数据库的"店铺"
            '成交时间': '交易时间'   # Excel中的"成交时间"映射到数据库的"交易时间"
        }

        # 数据库中有但Excel中没有的字段，设置默认值
        self.default_values = {
            '交易规格': None,
            '订单货品备注': None
        }

    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            if self.connection.is_connected():
                logging.info("成功连接到MySQL数据库")
                return True
        except Error as e:
            logging.error(f"数据库连接失败: {e}")
            return False

    def close_connection(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logging.info("数据库连接已关闭")

    def filter_valid_columns(self, df):
        """过滤掉无效的列名"""
        valid_columns = []
        for col in df.columns:
            col_str = str(col)
            # 跳过数字列名、nan列名等无效列
            if (col_str.isdigit() or 
                col_str.lower() in ['nan', 'unnamed'] or 
                col_str.startswith('nan.') or
                col_str.startswith('Unnamed')):
                logging.warning(f"跳过无效列: {col_str}")
                continue
            valid_columns.append(col)
        return valid_columns

    def process_excel_data(self, df):
        """处理Excel数据，转换为数据库格式"""
        # 过滤有效列
        valid_columns = self.filter_valid_columns(df)
        df_clean = df[valid_columns].copy()
        
        # 创建新的DataFrame用于数据库插入
        processed_data = pd.DataFrame()
        
        # 映射Excel列到数据库字段
        for excel_col, db_col in self.column_mapping.items():
            if db_col and excel_col in df_clean.columns:
                processed_data[db_col] = df_clean[excel_col]
        
        # 添加默认值字段
        for field, default_value in self.default_values.items():
            processed_data[field] = default_value
        
        # 数据类型转换和清理
        # 处理成本字段（可能包含"无权限查看"等文本）
        if '成本' in processed_data.columns:
            processed_data['成本'] = pd.to_numeric(processed_data['成本'], errors='coerce')
        
        # 处理数量字段
        if '数量' in processed_data.columns:
            processed_data['数量'] = pd.to_numeric(processed_data['数量'], errors='coerce')
        
        # 处理价格字段
        if '价格' in processed_data.columns:
            processed_data['价格'] = pd.to_numeric(processed_data['价格'], errors='coerce')

        # 处理预估重量字段
        if '预估重量' in processed_data.columns:
            processed_data['预估重量'] = pd.to_numeric(processed_data['预估重量'], errors='coerce')

        # 处理货品总数字段
        if '货品总数' in processed_data.columns:
            processed_data['货品总数'] = pd.to_numeric(processed_data['货品总数'], errors='coerce')

        # 处理货品样数字段
        if '货品样数' in processed_data.columns:
            processed_data['货品样数'] = pd.to_numeric(processed_data['货品样数'], errors='coerce')

        # 处理时间字段
        for time_col in ['交易时间', '发货时间', '审核时间']:
            if time_col in processed_data.columns:
                processed_data[time_col] = pd.to_datetime(processed_data[time_col], errors='coerce')
        
        return processed_data

    def insert_data_batch(self, data, batch_size=1000):
        """批量插入数据到数据库"""
        if not self.connection or not self.connection.is_connected():
            logging.error("数据库未连接")
            return False
        
        cursor = self.connection.cursor()
        
        try:
            # 构建插入SQL语句
            columns = list(data.columns)
            placeholders = ', '.join(['%s'] * len(columns))
            sql = f"INSERT INTO {self.table_name} ({', '.join([f'`{col}`' for col in columns])}) VALUES ({placeholders})"
            
            # 转换数据为列表格式
            data_list = []
            for _, row in data.iterrows():
                row_data = []
                for col in columns:
                    value = row[col]
                    if pd.isna(value):
                        row_data.append(None)
                    else:
                        row_data.append(value)
                data_list.append(tuple(row_data))
            
            # 批量插入
            total_rows = len(data_list)
            inserted_rows = 0
            
            for i in range(0, total_rows, batch_size):
                batch = data_list[i:i + batch_size]
                cursor.executemany(sql, batch)
                self.connection.commit()
                inserted_rows += len(batch)
                if inserted_rows % 5000 == 0 or inserted_rows == total_rows:
                    logging.info(f"已插入 {inserted_rows}/{total_rows} 行数据")
            
            logging.info(f"成功插入 {total_rows} 行数据")
            return True
            
        except Error as e:
            logging.error(f"数据插入失败: {e}")
            self.connection.rollback()
            return False
        finally:
            cursor.close()

    def import_excel_file(self, file_path):
        """导入单个Excel文件"""
        try:
            logging.info(f"开始处理文件: {os.path.basename(file_path)}")
            start_time = time.time()
            
            # 读取Excel文件
            df = pd.read_excel(file_path)
            logging.info(f"文件包含 {len(df)} 行数据，{len(df.columns)} 列")
            
            if len(df) == 0:
                logging.warning(f"文件 {file_path} 为空，跳过")
                return True
            
            # 处理数据
            processed_data = self.process_excel_data(df)
            logging.info(f"处理后数据: {len(processed_data)} 行，{len(processed_data.columns)} 列")
            
            # 插入数据库
            success = self.insert_data_batch(processed_data)
            
            elapsed_time = time.time() - start_time
            if success:
                logging.info(f"文件 {os.path.basename(file_path)} 导入成功，耗时 {elapsed_time:.2f} 秒")
            else:
                logging.error(f"文件 {os.path.basename(file_path)} 导入失败")
            
            return success
            
        except Exception as e:
            logging.error(f"处理文件 {file_path} 时出错: {e}")
            return False

    def get_database_record_count(self):
        """获取数据库当前记录数"""
        try:
            cursor = self.connection.cursor()
            cursor.execute(f"SELECT COUNT(*) FROM {self.table_name}")
            count = cursor.fetchone()[0]
            cursor.close()
            return count
        except:
            return 0

    def batch_import_all_files(self, data_dir='data'):
        """批量导入所有Excel文件"""
        if not self.connect_database():
            return False
        
        try:
            # 获取导入前的记录数
            initial_count = self.get_database_record_count()
            logging.info(f"导入前数据库记录数: {initial_count}")
            
            # 获取所有xlsx文件
            xlsx_files = glob.glob(os.path.join(data_dir, '*.xlsx'))
            xlsx_files.sort()  # 按文件名排序
            
            logging.info(f"找到 {len(xlsx_files)} 个Excel文件")
            
            success_count = 0
            failed_files = []
            total_start_time = time.time()
            
            for i, file_path in enumerate(xlsx_files, 1):
                logging.info(f"\n=== 处理第 {i}/{len(xlsx_files)} 个文件 ===")
                
                if self.import_excel_file(file_path):
                    success_count += 1
                else:
                    failed_files.append(file_path)
                
                # 每10个文件显示一次进度
                if i % 10 == 0:
                    current_count = self.get_database_record_count()
                    imported_records = current_count - initial_count
                    elapsed_time = time.time() - total_start_time
                    logging.info(f"进度: {i}/{len(xlsx_files)} 文件，已导入 {imported_records} 条记录，耗时 {elapsed_time:.2f} 秒")
            
            # 最终统计
            final_count = self.get_database_record_count()
            total_imported = final_count - initial_count
            total_elapsed = time.time() - total_start_time
            
            logging.info(f"\n=== 导入完成 ===")
            logging.info(f"成功导入: {success_count} 个文件")
            logging.info(f"失败文件: {len(failed_files)} 个")
            logging.info(f"总导入记录数: {total_imported}")
            logging.info(f"总耗时: {total_elapsed:.2f} 秒")
            
            if failed_files:
                logging.error("失败的文件:")
                for file_path in failed_files:
                    logging.error(f"  - {os.path.basename(file_path)}")
            
            return len(failed_files) == 0
            
        finally:
            self.close_connection()

def main():
    """主函数"""
    logging.info("开始批量导入Excel数据到MySQL数据库")
    logging.info("数据库配置: ************/qq_day_sale/qq_saledetail")
    
    importer = FinalExcelToMySQLImporter()
    success = importer.batch_import_all_files()
    
    if success:
        logging.info("所有文件导入成功！")
    else:
        logging.error("部分文件导入失败，请查看日志")

if __name__ == "__main__":
    main()
