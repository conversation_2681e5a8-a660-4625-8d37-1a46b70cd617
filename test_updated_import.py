#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的导入脚本
"""

import os
import pandas as pd
import mysql.connector
from mysql.connector import Error
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_database_structure():
    """测试数据库表结构"""
    try:
        connection = mysql.connector.connect(
            host='************',
            user='lxt',
            password='Lxt0307+',
            database='qq_day_sale',
            charset='utf8mb4'
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM qq_saledetail")
            count = cursor.fetchone()[0]
            logging.info(f"数据库连接成功，当前表中有 {count} 条记录")
            
            # 查看表结构
            cursor.execute("DESCRIBE qq_saledetail")
            columns = cursor.fetchall()
            logging.info(f"数据库表有 {len(columns)} 个字段:")
            for i, col in enumerate(columns, 1):
                logging.info(f"  {i:2d}. {col[0]}: {col[1]}")
            
            cursor.close()
            connection.close()
            return True
    except Error as e:
        logging.error(f"数据库连接失败: {e}")
        return False

def test_excel_processing():
    """测试Excel文件处理"""
    try:
        from batch_import_xlsx import ExcelToMySQLImporter
        
        # 选择一个测试文件
        test_files = [
            'data/20240103销售明细账.xlsx',
            'data/202401销售明细账.xlsx'
        ]
        
        test_file = None
        for file_path in test_files:
            if os.path.exists(file_path):
                test_file = file_path
                break
        
        if not test_file:
            logging.error("没有找到合适的测试文件")
            return False
        
        logging.info(f"使用测试文件: {test_file}")
        
        # 读取并分析文件
        df = pd.read_excel(test_file)
        logging.info(f"原始文件: {len(df)} 行, {len(df.columns)} 列")
        logging.info(f"原始列名: {list(df.columns)}")
        
        # 创建导入器并处理数据
        importer = ExcelToMySQLImporter()
        processed_data = importer.process_excel_data(df)
        
        logging.info(f"处理后数据: {len(processed_data)} 行, {len(processed_data.columns)} 列")
        logging.info(f"处理后列名: {list(processed_data.columns)}")
        
        # 检查数据类型
        logging.info("数据类型:")
        for col, dtype in processed_data.dtypes.items():
            logging.info(f"  {col}: {dtype}")
        
        # 显示前几行数据
        logging.info("前3行数据:")
        for i, row in processed_data.head(3).iterrows():
            logging.info(f"  行{i}: 订单编号={row.get('订单编号', 'N/A')}, 品名={row.get('品名', 'N/A')}, 数量={row.get('数量', 'N/A')}")
        
        return True
        
    except Exception as e:
        logging.error(f"Excel处理测试失败: {e}")
        return False

def test_small_import():
    """测试小量数据导入"""
    try:
        from batch_import_xlsx import ExcelToMySQLImporter
        
        # 选择一个小文件
        test_files = [
            'data/20240103销售明细账.xlsx',
            'data/202401销售明细账.xlsx'
        ]
        
        test_file = None
        for file_path in test_files:
            if os.path.exists(file_path):
                test_file = file_path
                break
        
        if not test_file:
            logging.error("没有找到合适的测试文件")
            return False
        
        logging.info(f"测试导入文件: {test_file}")
        
        # 创建导入器实例
        importer = ExcelToMySQLImporter()
        
        # 连接数据库
        if not importer.connect_database():
            return False
        
        try:
            # 获取导入前的记录数
            cursor = importer.connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM qq_saledetail")
            count_before = cursor.fetchone()[0]
            cursor.close()
            
            logging.info(f"导入前记录数: {count_before}")
            
            # 导入测试文件
            success = importer.import_excel_file(test_file)
            
            if success:
                # 获取导入后的记录数
                cursor = importer.connection.cursor()
                cursor.execute("SELECT COUNT(*) FROM qq_saledetail")
                count_after = cursor.fetchone()[0]
                cursor.close()
                
                imported_count = count_after - count_before
                logging.info(f"导入后记录数: {count_after}")
                logging.info(f"新增记录数: {imported_count}")
                logging.info("测试导入成功！")
            else:
                logging.error("测试导入失败")
            
            return success
            
        finally:
            importer.close_connection()
            
    except Exception as e:
        logging.error(f"测试导入时出错: {e}")
        return False

def main():
    """主测试函数"""
    logging.info("开始测试更新后的导入功能...")
    
    # 1. 测试数据库表结构
    logging.info("=== 测试数据库表结构 ===")
    if not test_database_structure():
        logging.error("数据库表结构测试失败")
        return
    
    # 2. 测试Excel文件处理
    logging.info("\n=== 测试Excel文件处理 ===")
    if not test_excel_processing():
        logging.error("Excel文件处理测试失败")
        return
    
    # 3. 测试小量数据导入
    logging.info("\n=== 测试数据导入 ===")
    if test_small_import():
        logging.info("所有测试通过！可以开始批量导入")
    else:
        logging.error("数据导入测试失败")

if __name__ == "__main__":
    main()
